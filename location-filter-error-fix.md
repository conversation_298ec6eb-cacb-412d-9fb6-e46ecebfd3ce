# Location Filter Error Fix

## Error Description
```
smart-dashboard.component.html:14 ERROR Error: Value must be an array in multiple-selection mode.
    at SmartDashboardComponent.setDefaultLocationSelection (smart-dashboard.component.ts:991:36)
    at SmartDashboardComponent.onDashboardChange (smart-dashboard.component.ts:1494:10)
```

This error occurred when switching to reconciliation dashboard because:
1. The form control was initialized as an array `[]`
2. When switching to reconciliation (single-select), we tried to set a string value
3. Angular Material detected the type mismatch and threw an error

## Root Cause
The issue was that Angular Material's mat-select in multiple mode expects the form control value to always be an array, but in single-select mode it expects a single value (string or null). When switching between dashboard types, we need to properly reset the form control type.

## Solution Implemented

### 1. Reverted Form Control Initialization
```typescript
// Reverted back to null to be compatible with both modes
readonly selectedLocationsCtrl = new FormControl<string[] | string | null>(null);
```

### 2. Enhanced setDefaultLocationSelection Method
```typescript
private setDefaultLocationSelection(): void {
  const isMultiSelect = this.selectedDashboard !== 'reconciliation';
  
  // First, reset the form control to ensure proper type
  if (isMultiSelect) {
    // For multi-select, ensure we start with an array
    const currentValue = this.selectedLocationsCtrl.value;
    if (!Array.isArray(currentValue)) {
      this.selectedLocationsCtrl.setValue([]);
    }
  } else {
    // For single-select, ensure we start with null or string
    const currentValue = this.selectedLocationsCtrl.value;
    if (Array.isArray(currentValue)) {
      this.selectedLocationsCtrl.setValue(null);
    }
  }
  
  // Then set the appropriate default values...
}
```

### 3. Added Safe Template Binding
```html
[placeholder]="'Select restaurants (' + (getSelectedLocationCount() || 0) + '/' + (filteredBranches?.length || 0) + ')'"
```

## How the Fix Works

1. **Type Safety**: Before setting any values, we check the current form control value type and reset it if it doesn't match the expected type for the current mode.

2. **Proper Initialization**: 
   - For multi-select mode: Ensure the form control has an array value before setting selections
   - For single-select mode: Ensure the form control has null/string value before setting selection

3. **Safe Template Access**: Added null checks in the template to prevent errors during initialization

## Testing Steps

1. Start on any non-reconciliation dashboard (should show multi-select)
2. Switch to reconciliation dashboard (should convert to single-select without error)
3. Switch back to non-reconciliation dashboard (should convert back to multi-select)
4. Verify no console errors occur during switching
5. Verify location counts display correctly in both modes

## Expected Behavior

- **Multi-select mode**: Shows "Select restaurants (X/Y)" with all restaurants selected by default
- **Single-select mode**: Shows single restaurant selection for reconciliation
- **Dashboard switching**: Smooth transition between modes without errors
- **API calls**: Continue to receive correct location data

The fix ensures that the form control type is always compatible with the current mat-select mode, preventing the "Value must be an array in multiple-selection mode" error.
